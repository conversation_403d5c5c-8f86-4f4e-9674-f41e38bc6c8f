const fs = require('fs');
const path = require('path');

/**
 * Supported code file extensions
 */
const SUPPORTED_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.html', '.css', '.php'];

/**
 * Maximum chunk size for file content (in characters)
 * This helps optimize embedding performance and stay within token limits
 */
const MAX_CHUNK_SIZE = 2000;

/**
 * Recursively reads all supported code files from a directory
 * @param {string} dirPath - The directory path to scan
 * @returns {Array} Array of file objects with path, content, and metadata
 */
function readCodeFilesRecursively(dirPath) {
  const files = [];
  
  try {
    if (!fs.existsSync(dirPath)) {
      throw new Error(`Directory does not exist: ${dirPath}`);
    }

    const stats = fs.statSync(dirPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path is not a directory: ${dirPath}`);
    }

    function scanDirectory(currentPath) {
      const items = fs.readdirSync(currentPath);
      
      for (const item of items) {
        const fullPath = path.join(currentPath, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          // Skip common directories that shouldn't be indexed
          if (!shouldSkipDirectory(item)) {
            scanDirectory(fullPath);
          }
        } else if (stats.isFile()) {
          const ext = path.extname(item).toLowerCase();
          if (SUPPORTED_EXTENSIONS.includes(ext)) {
            try {
              const content = fs.readFileSync(fullPath, 'utf8');
              const chunks = chunkContent(content, fullPath);
              files.push(...chunks);
            } catch (error) {
              console.warn(`Warning: Could not read file ${fullPath}:`, error.message);
            }
          }
        }
      }
    }

    scanDirectory(dirPath);
    return files;
  } catch (error) {
    throw new Error(`Error reading directory: ${error.message}`);
  }
}

/**
 * Determines if a directory should be skipped during scanning
 * @param {string} dirName - Directory name
 * @returns {boolean} True if directory should be skipped
 */
function shouldSkipDirectory(dirName) {
  const skipDirs = [
    'node_modules',
    '.git',
    '.svn',
    '.hg',
    'dist',
    'build',
    'coverage',
    '.nyc_output',
    'vendor',
    '.vscode',
    '.idea',
    '__pycache__',
    '.pytest_cache'
  ];
  
  return skipDirs.includes(dirName) || dirName.startsWith('.');
}

/**
 * Splits file content into chunks for better embedding performance
 * @param {string} content - File content
 * @param {string} filePath - File path for metadata
 * @returns {Array} Array of chunk objects
 */
function chunkContent(content, filePath) {
  if (content.length <= MAX_CHUNK_SIZE) {
    return [{
      path: filePath,
      content: content,
      chunkIndex: 0,
      totalChunks: 1,
      size: content.length,
      extension: path.extname(filePath).toLowerCase()
    }];
  }

  const chunks = [];
  const lines = content.split('\n');
  let currentChunk = '';
  let chunkIndex = 0;
  
  for (const line of lines) {
    if (currentChunk.length + line.length + 1 > MAX_CHUNK_SIZE && currentChunk.length > 0) {
      chunks.push({
        path: filePath,
        content: currentChunk.trim(),
        chunkIndex: chunkIndex,
        totalChunks: 0, // Will be updated after all chunks are created
        size: currentChunk.length,
        extension: path.extname(filePath).toLowerCase()
      });
      currentChunk = line;
      chunkIndex++;
    } else {
      currentChunk += (currentChunk ? '\n' : '') + line;
    }
  }
  
  // Add the last chunk if it has content
  if (currentChunk.trim()) {
    chunks.push({
      path: filePath,
      content: currentChunk.trim(),
      chunkIndex: chunkIndex,
      totalChunks: 0,
      size: currentChunk.length,
      extension: path.extname(filePath).toLowerCase()
    });
  }
  
  // Update totalChunks for all chunks
  chunks.forEach(chunk => {
    chunk.totalChunks = chunks.length;
  });
  
  return chunks;
}

module.exports = {
  readCodeFilesRecursively,
  SUPPORTED_EXTENSIONS,
  MAX_CHUNK_SIZE
};
