const OpenAI = require('openai');

/**
 * OpenAI client instance
 */
let openai = null;

/**
 * Initialize OpenAI client
 * @param {string} apiKey - OpenAI API key
 */
function initializeOpenAI(apiKey) {
  if (!apiKey) {
    throw new Error('OpenAI API key is required');
  }
  
  openai = new OpenAI({
    apiKey: apiKey
  });
}

/**
 * Create embeddings for text content using OpenAI's text-embedding-ada-002 model
 * @param {string} text - Text content to embed
 * @returns {Promise<Array>} Embedding vector
 */
async function createEmbedding(text) {
  if (!openai) {
    throw new Error('OpenAI client not initialized. Call initializeOpenAI() first.');
  }

  if (!text || typeof text !== 'string') {
    throw new Error('Text content is required and must be a string');
  }

  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: text.trim()
    });

    if (!response.data || !response.data[0] || !response.data[0].embedding) {
      throw new Error('Invalid response from OpenAI embeddings API');
    }

    return response.data[0].embedding;
  } catch (error) {
    if (error.response) {
      throw new Error(`OpenAI API error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to reach OpenAI API');
    } else {
      throw new Error(`Embedding creation failed: ${error.message}`);
    }
  }
}

/**
 * Create embeddings for multiple text chunks with rate limiting
 * @param {Array} chunks - Array of chunk objects with content
 * @param {Function} progressCallback - Optional callback for progress updates
 * @returns {Promise<Array>} Array of chunks with embeddings
 */
async function createEmbeddingsForChunks(chunks, progressCallback = null) {
  if (!Array.isArray(chunks)) {
    throw new Error('Chunks must be an array');
  }

  const chunksWithEmbeddings = [];
  const total = chunks.length;
  
  console.log(`Creating embeddings for ${total} chunks...`);

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    
    try {
      const embedding = await createEmbedding(chunk.content);
      
      chunksWithEmbeddings.push({
        ...chunk,
        embedding: embedding,
        embeddingCreatedAt: new Date().toISOString()
      });

      if (progressCallback) {
        progressCallback(i + 1, total);
      }

      // Add a small delay to respect rate limits
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

    } catch (error) {
      console.error(`Failed to create embedding for chunk ${i} (${chunk.path}):`, error.message);
      // Continue with other chunks even if one fails
      chunksWithEmbeddings.push({
        ...chunk,
        embedding: null,
        embeddingError: error.message,
        embeddingCreatedAt: new Date().toISOString()
      });
    }
  }

  const successCount = chunksWithEmbeddings.filter(chunk => chunk.embedding !== null).length;
  console.log(`Successfully created embeddings for ${successCount}/${total} chunks`);

  return chunksWithEmbeddings;
}

/**
 * Generate AI response using GPT-4o-mini with context chunks
 * @param {string} question - User question
 * @param {Array} contextChunks - Relevant code chunks for context
 * @returns {Promise<string>} AI-generated response
 */
async function generateAIResponse(question, contextChunks) {
  if (!openai) {
    throw new Error('OpenAI client not initialized. Call initializeOpenAI() first.');
  }

  if (!question || typeof question !== 'string') {
    throw new Error('Question is required and must be a string');
  }

  if (!Array.isArray(contextChunks)) {
    throw new Error('Context chunks must be an array');
  }

  try {
    // Prepare context from code chunks
    const contextText = contextChunks.map((chunk, index) => {
      const chunkInfo = chunk.totalChunks > 1 
        ? ` (chunk ${chunk.chunkIndex + 1}/${chunk.totalChunks})`
        : '';
      
      return `--- Code Chunk ${index + 1}: ${chunk.path}${chunkInfo} ---\n${chunk.content}\n`;
    }).join('\n');

    const systemPrompt = `You are an AI assistant that helps developers understand and work with code. You will be provided with relevant code chunks from a codebase and a user question. Analyze the code and provide a helpful, accurate response.

Guidelines:
- Focus on the specific question asked
- Reference specific parts of the code when relevant
- Explain concepts clearly and concisely
- If the provided code doesn't contain enough information to answer the question, say so
- Provide code examples when helpful
- Be precise about file names and locations when referencing code`;

    const userPrompt = `Based on the following code chunks, please answer this question: ${question}

Relevant Code:
${contextText}

Please provide a comprehensive answer based on the code provided above.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: 1500,
      temperature: 0.1
    });

    if (!response.choices || !response.choices[0] || !response.choices[0].message) {
      throw new Error('Invalid response from OpenAI chat API');
    }

    return response.choices[0].message.content.trim();
  } catch (error) {
    if (error.response) {
      throw new Error(`OpenAI API error: ${error.response.status} - ${error.response.data?.error?.message || 'Unknown error'}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to reach OpenAI API');
    } else {
      throw new Error(`AI response generation failed: ${error.message}`);
    }
  }
}

module.exports = {
  initializeOpenAI,
  createEmbedding,
  createEmbeddingsForChunks,
  generateAIResponse
};
