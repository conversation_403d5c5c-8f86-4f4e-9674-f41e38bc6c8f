# Code Search AI Backend

A Node.js Express server that provides AI-powered code search and question-answering capabilities using OpenAI embeddings and GPT-4o-mini.

## Features

- 🔍 **Recursive Code Scanning**: Automatically scans project directories for supported code files
- 📊 **Smart Chunking**: Splits large files into optimal chunks for embedding processing
- 🧠 **AI Embeddings**: Uses OpenAI's text-embedding-ada-002 model for semantic search
- 🎯 **Similarity Search**: Finds the most relevant code chunks using cosine similarity
- 💬 **AI Q&A**: Generates contextual answers using GPT-4o-mini with relevant code context
- 🚀 **RESTful API**: Simple POST endpoints for ingestion and querying
- ⚡ **In-Memory Storage**: Fast retrieval with in-memory embedding storage
- 🛡️ **Error Handling**: Comprehensive error handling and validation

## Supported File Types

- JavaScript (`.js`, `.jsx`)
- TypeScript (`.ts`, `.tsx`)
- HTML (`.html`)
- CSS (`.css`)
- PHP (`.php`)

## Prerequisites

- Node.js 16.0.0 or higher
- OpenAI API key

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit .env and add your OpenAI API key
   OPENAI_API_KEY=your_openai_api_key_here
   PORT=3000
   NODE_ENV=production
   ```

4. **Start the server**:
   ```bash
   # Production mode
   npm start
   
   # Development mode with auto-restart
   npm run dev
   ```

The server will start on `http://localhost:3000` (or the port specified in your `.env` file).

## API Endpoints

### Health Check
```http
GET /health
```
Returns server status and basic embedding information.

### Get Embeddings Info
```http
GET /info
```
Returns detailed information about current embeddings and statistics.

### Ingest Project
```http
POST /ingest
Content-Type: application/json

{
  "projectPath": "/path/to/your/project"
}
```

**Description**: Scans the specified directory recursively, reads all supported code files, creates embeddings, and stores them in memory.

**Response**:
```json
{
  "success": true,
  "message": "Project ingestion completed successfully",
  "statistics": {
    "totalChunks": 150,
    "successfulEmbeddings": 148,
    "failedEmbeddings": 2,
    "totalFiles": 45,
    "processingTimeSeconds": 23.5
  },
  "metadata": {
    "projectPath": "/absolute/path/to/project",
    "totalFiles": 45,
    "totalChunks": 150,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "lastUpdated": "2024-01-15T10:30:00.000Z"
  }
}
```

### Query Code
```http
POST /query
Content-Type: application/json

{
  "question": "How does user authentication work in this codebase?",
  "topK": 3
}
```

**Description**: Finds the most similar code chunks to your question and generates an AI-powered answer with context.

**Parameters**:
- `question` (required): Your question about the codebase
- `topK` (optional): Number of most similar chunks to include (default: 3, max: 10)

**Response**:
```json
{
  "success": true,
  "question": "How does user authentication work in this codebase?",
  "answer": "Based on the code provided, user authentication in this codebase works through...",
  "context": {
    "relevantChunks": [
      {
        "path": "/project/auth/login.js",
        "chunkIndex": 0,
        "totalChunks": 1,
        "similarityScore": 0.892,
        "extension": ".js",
        "size": 1250
      }
    ],
    "similarityStats": {
      "count": 148,
      "min": 0.123,
      "max": 0.892,
      "mean": 0.445,
      "median": 0.432
    },
    "searchParameters": {
      "topK": 3,
      "totalAvailableChunks": 148
    }
  },
  "metadata": {
    "timestamp": "2024-01-15T10:35:00.000Z",
    "projectPath": "/absolute/path/to/project"
  }
}
```

## Usage Examples

### Using curl

1. **Ingest a project**:
   ```bash
   curl -X POST http://localhost:3000/ingest \
     -H "Content-Type: application/json" \
     -d '{"projectPath": "/path/to/your/project"}'
   ```

2. **Ask a question**:
   ```bash
   curl -X POST http://localhost:3000/query \
     -H "Content-Type: application/json" \
     -d '{"question": "How is error handling implemented?", "topK": 5}'
   ```

### Using JavaScript/Node.js

```javascript
const axios = require('axios');

const baseURL = 'http://localhost:3000';

// Ingest project
async function ingestProject(projectPath) {
  try {
    const response = await axios.post(`${baseURL}/ingest`, {
      projectPath: projectPath
    });
    console.log('Ingestion completed:', response.data);
  } catch (error) {
    console.error('Ingestion failed:', error.response?.data || error.message);
  }
}

// Query code
async function queryCode(question) {
  try {
    const response = await axios.post(`${baseURL}/query`, {
      question: question,
      topK: 3
    });
    console.log('Answer:', response.data.answer);
    console.log('Relevant files:', response.data.context.relevantChunks.map(c => c.path));
  } catch (error) {
    console.error('Query failed:', error.response?.data || error.message);
  }
}

// Example usage
ingestProject('/path/to/your/project')
  .then(() => queryCode('How does the main application start?'));
```

## Configuration

### Environment Variables

- `OPENAI_API_KEY` (required): Your OpenAI API key
- `PORT` (optional): Server port (default: 3000)
- `NODE_ENV` (optional): Environment mode (development/production)

### Customization

You can modify the following constants in the source code:

- **Supported file extensions**: Edit `SUPPORTED_EXTENSIONS` in `utils/fileReader.js`
- **Chunk size**: Modify `MAX_CHUNK_SIZE` in `utils/fileReader.js`
- **Embedding model**: Change the model in `utils/embeddings.js`
- **AI model**: Update the model in `generateAIResponse()` function

## Error Handling

The API provides detailed error messages for common issues:

- Invalid or missing project paths
- OpenAI API errors (rate limits, invalid keys, etc.)
- File reading permissions
- Network connectivity issues
- Invalid request formats

## Performance Considerations

- **Rate Limiting**: The server includes built-in delays to respect OpenAI's rate limits
- **Memory Usage**: Embeddings are stored in memory for fast retrieval
- **Chunk Size**: Files are automatically chunked to optimize embedding performance
- **Concurrent Requests**: The server can handle multiple query requests simultaneously

## Troubleshooting

### Common Issues

1. **"OpenAI API key is required"**
   - Ensure your `.env` file contains a valid `OPENAI_API_KEY`

2. **"Directory does not exist"**
   - Verify the project path exists and is accessible
   - Use absolute paths for better reliability

3. **"No supported code files found"**
   - Check that your project contains files with supported extensions
   - Verify directory permissions

4. **Rate limit errors**
   - The server includes automatic delays, but very large projects may hit limits
   - Consider processing smaller directories or implementing longer delays

### Debug Mode

Set `NODE_ENV=development` in your `.env` file for more detailed error messages and logging.

## License

MIT License - feel free to use this code for your projects!

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.
