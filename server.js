require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');

// Import utility modules
const { readCodeFilesRecursively, SUPPORTED_EXTENSIONS } = require('./utils/fileReader');
const { initializeOpenAI, createEmbedding, createEmbeddingsForChunks, generateAIResponse } = require('./utils/embeddings');
const { findSimilarChunks, calculateSimilarityStats } = require('./utils/similarity');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// In-memory storage for embeddings
let embeddingsStore = {
  chunks: [],
  metadata: {
    projectPath: null,
    totalFiles: 0,
    totalChunks: 0,
    createdAt: null,
    lastUpdated: null
  }
};

// Initialize OpenAI
try {
  if (!process.env.OPENAI_API_KEY) {
    console.error('ERROR: OPENAI_API_KEY environment variable is required');
    process.exit(1);
  }
  initializeOpenAI(process.env.OPENAI_API_KEY);
  console.log('OpenAI client initialized successfully');
} catch (error) {
  console.error('Failed to initialize OpenAI client:', error.message);
  process.exit(1);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    embeddings: {
      totalChunks: embeddingsStore.chunks.length,
      projectPath: embeddingsStore.metadata.projectPath,
      lastUpdated: embeddingsStore.metadata.lastUpdated
    }
  });
});

// Get current embeddings info
app.get('/info', (req, res) => {
  const validChunks = embeddingsStore.chunks.filter(chunk => chunk.embedding !== null);
  const fileStats = {};
  
  embeddingsStore.chunks.forEach(chunk => {
    const ext = chunk.extension;
    if (!fileStats[ext]) {
      fileStats[ext] = { files: new Set(), chunks: 0 };
    }
    fileStats[ext].files.add(chunk.path);
    fileStats[ext].chunks++;
  });

  // Convert sets to counts
  Object.keys(fileStats).forEach(ext => {
    fileStats[ext].files = fileStats[ext].files.size;
  });

  res.json({
    metadata: embeddingsStore.metadata,
    statistics: {
      totalChunks: embeddingsStore.chunks.length,
      validEmbeddings: validChunks.length,
      failedEmbeddings: embeddingsStore.chunks.length - validChunks.length,
      supportedExtensions: SUPPORTED_EXTENSIONS,
      fileStatistics: fileStats
    }
  });
});

// POST /ingest - Process project directory and create embeddings
app.post('/ingest', async (req, res) => {
  try {
    const { projectPath } = req.body;

    // Validation
    if (!projectPath) {
      return res.status(400).json({
        error: 'Project path is required',
        message: 'Please provide a projectPath in the request body'
      });
    }

    if (typeof projectPath !== 'string') {
      return res.status(400).json({
        error: 'Invalid project path',
        message: 'Project path must be a string'
      });
    }

    console.log(`Starting ingestion for project: ${projectPath}`);

    // Read all code files
    let chunks;
    try {
      chunks = readCodeFilesRecursively(projectPath);
    } catch (error) {
      return res.status(400).json({
        error: 'Failed to read project directory',
        message: error.message
      });
    }

    if (chunks.length === 0) {
      return res.status(400).json({
        error: 'No supported code files found',
        message: `No files with supported extensions (${SUPPORTED_EXTENSIONS.join(', ')}) were found in the specified directory`
      });
    }

    console.log(`Found ${chunks.length} code chunks from project`);

    // Create embeddings for all chunks
    const startTime = Date.now();
    const chunksWithEmbeddings = await createEmbeddingsForChunks(chunks, (current, total) => {
      if (current % 10 === 0 || current === total) {
        console.log(`Progress: ${current}/${total} embeddings created`);
      }
    });

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;

    // Update in-memory store
    embeddingsStore.chunks = chunksWithEmbeddings;
    embeddingsStore.metadata = {
      projectPath: path.resolve(projectPath),
      totalFiles: new Set(chunks.map(chunk => chunk.path)).size,
      totalChunks: chunks.length,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      processingTimeSeconds: processingTime
    };

    const validEmbeddings = chunksWithEmbeddings.filter(chunk => chunk.embedding !== null).length;

    console.log(`Ingestion completed: ${validEmbeddings}/${chunks.length} embeddings created in ${processingTime}s`);

    res.json({
      success: true,
      message: 'Project ingestion completed successfully',
      statistics: {
        totalChunks: chunks.length,
        successfulEmbeddings: validEmbeddings,
        failedEmbeddings: chunks.length - validEmbeddings,
        totalFiles: embeddingsStore.metadata.totalFiles,
        processingTimeSeconds: processingTime
      },
      metadata: embeddingsStore.metadata
    });

  } catch (error) {
    console.error('Ingestion error:', error);
    res.status(500).json({
      error: 'Internal server error during ingestion',
      message: error.message
    });
  }
});

// POST /query - Answer user questions using embeddings
app.post('/query', async (req, res) => {
  try {
    const { question, topK = 3 } = req.body;

    // Validation
    if (!question) {
      return res.status(400).json({
        error: 'Question is required',
        message: 'Please provide a question in the request body'
      });
    }

    if (typeof question !== 'string') {
      return res.status(400).json({
        error: 'Invalid question format',
        message: 'Question must be a string'
      });
    }

    if (embeddingsStore.chunks.length === 0) {
      return res.status(400).json({
        error: 'No embeddings available',
        message: 'Please ingest a project first using the /ingest endpoint'
      });
    }

    const validChunks = embeddingsStore.chunks.filter(chunk => chunk.embedding !== null);
    if (validChunks.length === 0) {
      return res.status(400).json({
        error: 'No valid embeddings available',
        message: 'All embeddings failed during ingestion. Please try ingesting the project again.'
      });
    }

    console.log(`Processing query: "${question}"`);

    // Create embedding for the question
    let questionEmbedding;
    try {
      questionEmbedding = await createEmbedding(question);
    } catch (error) {
      return res.status(500).json({
        error: 'Failed to create question embedding',
        message: error.message
      });
    }

    // Find most similar chunks
    const similarChunks = findSimilarChunks(questionEmbedding, validChunks, Math.min(topK, 10));

    if (similarChunks.length === 0) {
      return res.status(500).json({
        error: 'No similar chunks found',
        message: 'Unable to find relevant code chunks for the question'
      });
    }

    // Generate AI response
    let aiResponse;
    try {
      aiResponse = await generateAIResponse(question, similarChunks);
    } catch (error) {
      return res.status(500).json({
        error: 'Failed to generate AI response',
        message: error.message
      });
    }

    // Calculate similarity statistics
    const stats = calculateSimilarityStats(questionEmbedding, validChunks);

    console.log(`Query completed successfully. Found ${similarChunks.length} relevant chunks.`);

    res.json({
      success: true,
      question: question,
      answer: aiResponse,
      context: {
        relevantChunks: similarChunks.map(chunk => ({
          path: chunk.path,
          chunkIndex: chunk.chunkIndex,
          totalChunks: chunk.totalChunks,
          similarityScore: Math.round(chunk.similarityScore * 1000) / 1000,
          extension: chunk.extension,
          size: chunk.size
        })),
        similarityStats: stats,
        searchParameters: {
          topK: topK,
          totalAvailableChunks: validChunks.length
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        projectPath: embeddingsStore.metadata.projectPath
      }
    });

  } catch (error) {
    console.error('Query error:', error);
    res.status(500).json({
      error: 'Internal server error during query processing',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `The endpoint ${req.method} ${req.path} does not exist`,
    availableEndpoints: [
      'GET /health',
      'GET /info',
      'POST /ingest',
      'POST /query'
    ]
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`\n🚀 Code Search AI Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Info endpoint: http://localhost:${PORT}/info`);
  console.log(`📥 Ingest endpoint: POST http://localhost:${PORT}/ingest`);
  console.log(`❓ Query endpoint: POST http://localhost:${PORT}/query`);
  console.log(`\nSupported file extensions: ${SUPPORTED_EXTENSIONS.join(', ')}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'production'}\n`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  process.exit(0);
});
