<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .user-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .form-group {
            margin: 10px 0;
        }
        input, button {
            padding: 8px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>User Management System</h1>
    
    <div id="user-form">
        <h2>Add New User</h2>
        <div class="form-group">
            <input type="text" id="name" placeholder="Name" required>
        </div>
        <div class="form-group">
            <input type="email" id="email" placeholder="Email" required>
        </div>
        <button onclick="addUser()">Add User</button>
    </div>
    
    <div id="users-list">
        <h2>Users</h2>
        <div id="users-container"></div>
    </div>

    <script>
        async function loadUsers() {
            try {
                const response = await fetch('/users');
                const users = await response.json();
                displayUsers(users);
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        function displayUsers(users) {
            const container = document.getElementById('users-container');
            container.innerHTML = '';
            
            users.forEach(user => {
                const userCard = document.createElement('div');
                userCard.className = 'user-card';
                userCard.innerHTML = `
                    <h3>${user.name}</h3>
                    <p>Email: ${user.email}</p>
                    <p>ID: ${user.id}</p>
                `;
                container.appendChild(userCard);
            });
        }

        async function addUser() {
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            
            if (!name || !email) {
                alert('Please fill in all fields');
                return;
            }
            
            try {
                const response = await fetch('/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email })
                });
                
                if (response.ok) {
                    document.getElementById('name').value = '';
                    document.getElementById('email').value = '';
                    loadUsers();
                } else {
                    alert('Error adding user');
                }
            } catch (error) {
                console.error('Error adding user:', error);
            }
        }

        // Load users when page loads
        loadUsers();
    </script>
</body>
</html>
