/**
 * Calculate cosine similarity between two vectors
 * @param {Array} vectorA - First embedding vector
 * @param {Array} vectorB - Second embedding vector
 * @returns {number} Cosine similarity score (0 to 1)
 */
function cosineSimilarity(vectorA, vectorB) {
  if (!Array.isArray(vectorA) || !Array.isArray(vectorB)) {
    throw new Error('Both vectors must be arrays');
  }

  if (vectorA.length !== vectorB.length) {
    throw new Error('Vectors must have the same length');
  }

  if (vectorA.length === 0) {
    throw new Error('Vectors cannot be empty');
  }

  let dotProduct = 0;
  let magnitudeA = 0;
  let magnitudeB = 0;

  for (let i = 0; i < vectorA.length; i++) {
    dotProduct += vectorA[i] * vectorB[i];
    magnitudeA += vectorA[i] * vectorA[i];
    magnitudeB += vectorB[i] * vectorB[i];
  }

  magnitudeA = Math.sqrt(magnitudeA);
  magnitudeB = Math.sqrt(magnitudeB);

  if (magnitudeA === 0 || magnitudeB === 0) {
    return 0;
  }

  return dotProduct / (magnitudeA * magnitudeB);
}

/**
 * Find the most similar chunks to a query embedding
 * @param {Array} queryEmbedding - Query embedding vector
 * @param {Array} chunks - Array of chunks with embeddings
 * @param {number} topK - Number of top results to return (default: 3)
 * @returns {Array} Array of top similar chunks with similarity scores
 */
function findSimilarChunks(queryEmbedding, chunks, topK = 3) {
  if (!Array.isArray(queryEmbedding)) {
    throw new Error('Query embedding must be an array');
  }

  if (!Array.isArray(chunks)) {
    throw new Error('Chunks must be an array');
  }

  if (topK <= 0) {
    throw new Error('topK must be a positive number');
  }

  // Filter chunks that have valid embeddings
  const validChunks = chunks.filter(chunk => 
    chunk.embedding && 
    Array.isArray(chunk.embedding) && 
    chunk.embedding.length > 0
  );

  if (validChunks.length === 0) {
    return [];
  }

  // Calculate similarity scores for all chunks
  const chunksWithScores = validChunks.map(chunk => {
    try {
      const similarity = cosineSimilarity(queryEmbedding, chunk.embedding);
      return {
        ...chunk,
        similarityScore: similarity
      };
    } catch (error) {
      console.warn(`Error calculating similarity for chunk ${chunk.path}:`, error.message);
      return {
        ...chunk,
        similarityScore: 0
      };
    }
  });

  // Sort by similarity score (highest first) and return top K
  return chunksWithScores
    .sort((a, b) => b.similarityScore - a.similarityScore)
    .slice(0, topK);
}

/**
 * Calculate statistics for similarity scores
 * @param {Array} queryEmbedding - Query embedding vector
 * @param {Array} chunks - Array of chunks with embeddings
 * @returns {Object} Statistics object with min, max, mean, and median scores
 */
function calculateSimilarityStats(queryEmbedding, chunks) {
  if (!Array.isArray(queryEmbedding) || !Array.isArray(chunks)) {
    throw new Error('Invalid input parameters');
  }

  const validChunks = chunks.filter(chunk => 
    chunk.embedding && 
    Array.isArray(chunk.embedding) && 
    chunk.embedding.length > 0
  );

  if (validChunks.length === 0) {
    return {
      count: 0,
      min: 0,
      max: 0,
      mean: 0,
      median: 0
    };
  }

  const scores = validChunks.map(chunk => {
    try {
      return cosineSimilarity(queryEmbedding, chunk.embedding);
    } catch (error) {
      return 0;
    }
  }).filter(score => !isNaN(score));

  if (scores.length === 0) {
    return {
      count: 0,
      min: 0,
      max: 0,
      mean: 0,
      median: 0
    };
  }

  scores.sort((a, b) => a - b);

  const min = scores[0];
  const max = scores[scores.length - 1];
  const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  const median = scores.length % 2 === 0
    ? (scores[scores.length / 2 - 1] + scores[scores.length / 2]) / 2
    : scores[Math.floor(scores.length / 2)];

  return {
    count: scores.length,
    min: Math.round(min * 1000) / 1000,
    max: Math.round(max * 1000) / 1000,
    mean: Math.round(mean * 1000) / 1000,
    median: Math.round(median * 1000) / 1000
  };
}

module.exports = {
  cosineSimilarity,
  findSimilarChunks,
  calculateSimilarityStats
};
