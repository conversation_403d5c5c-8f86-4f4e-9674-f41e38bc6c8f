// Test script for the AI Code Assistant API
const axios = require('axios');

const baseURL = 'http://localhost:3000';

async function testIngest() {
  console.log('🔍 Testing /ingest endpoint...');
  
  try {
    const response = await axios.post(`${baseURL}/ingest`, {
      projectPath: '.'
    });
    
    console.log('✅ Ingestion successful!');
    console.log('📊 Statistics:', response.data.statistics);
    return true;
  } catch (error) {
    console.error('❌ Ingestion failed:', error.response?.data || error.message);
    return false;
  }
}

async function testQuery(question) {
  console.log(`\n❓ Testing query: "${question}"`);
  
  try {
    const response = await axios.post(`${baseURL}/query`, {
      question: question
    });
    
    console.log('✅ Query successful!');
    console.log('🤖 Answer:', response.data.answer);
    console.log('📁 Relevant files:', response.data.context.map(c => c.path));
    console.log('🎯 Similarity scores:', response.data.context.map(c => c.similarityScore));
    return true;
  } catch (error) {
    console.error('❌ Query failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting AI Code Assistant Tests\n');
  
  // Test ingestion
  const ingestSuccess = await testIngest();
  
  if (!ingestSuccess) {
    console.log('❌ Cannot proceed with queries - ingestion failed');
    return;
  }
  
  // Wait a moment for ingestion to complete
  console.log('\n⏳ Waiting for ingestion to complete...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test queries
  const questions = [
    'How does the Express server work in this codebase?',
    'What are the main API endpoints?',
    'How is user management implemented?',
    'What HTML elements are used in the frontend?'
  ];
  
  for (const question of questions) {
    await testQuery(question);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay between queries
  }
  
  console.log('\n🎉 All tests completed!');
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite failed:', error.message);
});
