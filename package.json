{"name": "code-search-ai-backend", "version": "1.0.0", "description": "Node.js backend server for AI-powered code search using OpenAI embeddings", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "openai", "embeddings", "code-search", "ai"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "openai": "^4.20.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}