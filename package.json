{"name": "ai-code-assistant", "version": "1.0.0", "description": "AI-powered code assistant like Cursor - Node.js backend with OpenAI embeddings and GPT-4o", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "code-assistant", "cursor", "openai", "embeddings", "gpt-4o", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "openai": "^4.20.1", "tiktoken": "^1.0.10"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}