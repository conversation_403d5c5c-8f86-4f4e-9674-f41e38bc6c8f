// Configuration
const API_BASE_URL = 'http://localhost:3002';

// DOM Elements
const statusIndicator = document.getElementById('statusIndicator');
const statusDot = statusIndicator.querySelector('.status-dot');
const statusText = statusIndicator.querySelector('.status-text');
const projectPathInput = document.getElementById('projectPath');
const ingestBtn = document.getElementById('ingestBtn');
const questionInput = document.getElementById('questionInput');
const askBtn = document.getElementById('askBtn');
const chatMessages = document.getElementById('chatMessages');
const clearChatBtn = document.getElementById('clearChat');
const loadingOverlay = document.getElementById('loadingOverlay');
const loadingText = document.getElementById('loadingText');
const toastContainer = document.getElementById('toastContainer');

// Stats elements
const totalFilesEl = document.getElementById('totalFiles');
const totalChunksEl = document.getElementById('totalChunks');
const validEmbeddingsEl = document.getElementById('validEmbeddings');

// State
let isConnected = false;
let isReady = false;

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    checkServerStatus();
    setupEventListeners();
    
    // Check server status every 30 seconds
    setInterval(checkServerStatus, 30000);
});

// Event Listeners
function setupEventListeners() {
    ingestBtn.addEventListener('click', handleIngest);
    askBtn.addEventListener('click', handleAsk);
    clearChatBtn.addEventListener('click', clearChat);
    
    questionInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!askBtn.disabled) {
                handleAsk();
            }
        }
    });
    
    questionInput.addEventListener('input', () => {
        askBtn.disabled = !questionInput.value.trim() || !isReady;
    });
}

// Server Status Check
async function checkServerStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/status`);
        const data = await response.json();
        
        updateConnectionStatus(true);
        updateStats(data.statistics);
        updateReadyStatus(data.isReady);
        
    } catch (error) {
        updateConnectionStatus(false);
        updateReadyStatus(false);
    }
}

function updateConnectionStatus(connected) {
    isConnected = connected;
    
    if (connected) {
        statusDot.classList.add('connected');
        statusText.textContent = 'Connected';
    } else {
        statusDot.classList.remove('connected');
        statusText.textContent = 'Disconnected';
    }
}

function updateReadyStatus(ready) {
    isReady = ready;
    askBtn.disabled = !questionInput.value.trim() || !isReady;
    
    if (ready && chatMessages.querySelector('.welcome-message')) {
        showToast('Ready!', 'Project ingested successfully. You can now ask questions.', 'success');
    }
}

function updateStats(stats) {
    if (stats) {
        totalFilesEl.textContent = Object.values(stats.fileStatistics || {})
            .reduce((sum, stat) => sum + (stat.files || 0), 0);
        totalChunksEl.textContent = stats.totalChunks || 0;
        validEmbeddingsEl.textContent = stats.validEmbeddings || 0;
    }
}

// Ingest Project
async function handleIngest() {
    const projectPath = projectPathInput.value.trim();
    
    if (!projectPath) {
        showToast('Error', 'Please enter a project path', 'error');
        return;
    }
    
    if (!isConnected) {
        showToast('Error', 'Not connected to server', 'error');
        return;
    }
    
    showLoading('Reading project files...');
    ingestBtn.disabled = true;
    
    try {
        const response = await fetch(`${API_BASE_URL}/ingest`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ projectPath })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast('Success!', `Ingested ${data.statistics.totalFiles} files with ${data.statistics.successfulEmbeddings} embeddings`, 'success');
            checkServerStatus(); // Refresh stats
        } else {
            throw new Error(data.message || 'Ingestion failed');
        }
        
    } catch (error) {
        console.error('Ingestion error:', error);
        showToast('Error', error.message || 'Failed to ingest project', 'error');
    } finally {
        hideLoading();
        ingestBtn.disabled = false;
    }
}

// Ask Question
async function handleAsk() {
    const question = questionInput.value.trim();
    
    if (!question) return;
    if (!isConnected) {
        showToast('Error', 'Not connected to server', 'error');
        return;
    }
    if (!isReady) {
        showToast('Error', 'Please ingest a project first', 'error');
        return;
    }
    
    // Clear welcome message if it exists
    const welcomeMessage = chatMessages.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    // Add user message
    addMessage(question, 'user');
    questionInput.value = '';
    askBtn.disabled = true;
    
    showLoading('Thinking...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/query`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ question })
        });
        
        const data = await response.json();
        
        if (data.success) {
            addMessage(data.answer, 'ai', data.context);
        } else {
            throw new Error(data.message || 'Query failed');
        }
        
    } catch (error) {
        console.error('Query error:', error);
        addMessage(`Sorry, I encountered an error: ${error.message}`, 'ai');
        showToast('Error', error.message || 'Failed to get AI response', 'error');
    } finally {
        hideLoading();
        askBtn.disabled = !questionInput.value.trim() || !isReady;
    }
}

// Add Message to Chat
function addMessage(text, sender, context = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${sender}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const textDiv = document.createElement('div');
    textDiv.className = 'message-text';
    textDiv.textContent = text;
    
    contentDiv.appendChild(textDiv);
    
    // Add context for AI messages
    if (sender === 'ai' && context && context.length > 0) {
        const contextDiv = document.createElement('div');
        contextDiv.className = 'message-context';
        
        const titleDiv = document.createElement('div');
        titleDiv.className = 'context-title';
        titleDiv.textContent = 'Referenced files:';
        
        const filesDiv = document.createElement('div');
        filesDiv.className = 'context-files';
        
        context.forEach(chunk => {
            const fileSpan = document.createElement('span');
            fileSpan.className = 'context-file';
            fileSpan.textContent = `${chunk.path} (${(chunk.similarityScore * 100).toFixed(1)}%)`;
            filesDiv.appendChild(fileSpan);
        });
        
        contextDiv.appendChild(titleDiv);
        contextDiv.appendChild(filesDiv);
        contentDiv.appendChild(contextDiv);
    }
    
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Clear Chat
function clearChat() {
    chatMessages.innerHTML = `
        <div class="welcome-message">
            <div class="welcome-icon">🚀</div>
            <h3>Welcome to AI Code Assistant!</h3>
            <p>Start by ingesting your project, then ask questions about your codebase.</p>
            <div class="example-questions">
                <h4>Example questions:</h4>
                <ul>
                    <li>"How does authentication work in this app?"</li>
                    <li>"What are the main API endpoints?"</li>
                    <li>"How is error handling implemented?"</li>
                    <li>"Show me the database schema"</li>
                </ul>
            </div>
        </div>
    `;
}

// Loading Overlay
function showLoading(text = 'Processing...') {
    loadingText.textContent = text;
    loadingOverlay.classList.add('show');
}

function hideLoading() {
    loadingOverlay.classList.remove('show');
}

// Toast Notifications
function showToast(title, message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    toast.innerHTML = `
        <div class="toast-title">${title}</div>
        <div class="toast-message">${message}</div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        toast.remove();
    }, 5000);
    
    // Remove on click
    toast.addEventListener('click', () => {
        toast.remove();
    });
}
